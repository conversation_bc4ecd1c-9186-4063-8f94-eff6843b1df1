import SwiftUI
import SwiftData

struct ChatItemView: View {
    // MARK: - Properties
    @Bindable var item: ChatItem
    @EnvironmentObject var pomodoroManager: PomodoroManager
    @State private var isCompleted: Bool
    @State private var showingOptions = false
    
    @State private var isDragging = false
    @State private var dragOffset: CGSize = .zero
    @State private var isOverTrash = false
    
    var onEdit: () -> Void
    var onDelete: () -> Void
    
    @AppStorage("reverseAlignment") private var reverseAlignment = false
    @AppStorage("bubbleFontSize") private var bubbleFontSize: Double = 16
    
    @State private var isFullScreen = false
    
    @Environment(\.colorScheme) private var colorScheme
    
    @State private var showImageViewer = false
    @State private var selectedImageIndex = 0
    
    @State private var showCustomTimePicker = false
    @State private var showCustomDatePicker = false
    
    // 添加新的状态变量
    @State private var showCustomPomodoroCount = false
    @State private var tempPomodoroCount: Int = 1
    
    // MARK: - Initializer
    init(item: ChatItem, onEdit: @escaping () -> Void, onDelete: @escaping () -> Void) {
        self.item = item
        self._isCompleted = State(initialValue: item.completed)
        self.onEdit = onEdit
        self.onDelete = onDelete
    }
    
    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading) {
            ZStack {
                HStack(alignment: .top) {
                    if item.type == .note {
                        noteBubble
                        Spacer()
                    } else if item.type == .task {
                        Spacer()
                        taskBubble
                    } else if item.type == .expense {
                        if item.amount ?? 0 >= 0 {
                            Spacer()
                            incomeBubble
                        } else {
                            expenseBubble
                            Spacer()
                        }
                    }
                }
                .padding(.horizontal)
                .offset(dragOffset)
                
                if isDragging {
                    //trashView
                }
            }
        }
        .sheet(isPresented: $showCustomTimePicker) {
            VStack {
                DatePicker("select_time".localized, selection: Binding(
                    get: { self.item.plannedTime ?? Date() },
                    set: { newDate in
                        let components = Calendar.current.dateComponents([.hour, .minute], from: newDate)
                        self.setTaskTime(hour: components.hour ?? 0, minute: components.minute ?? 0)
                    }
                ), displayedComponents: .hourAndMinute)
                .datePickerStyle(WheelDatePickerStyle())
                .labelsHidden()
                
                Button("confirm_button".localized) {
                    showCustomTimePicker = false
                }
            }
            .padding()
        }
        .sheet(isPresented: $showCustomDatePicker) {
            VStack {
                DatePicker("select_date_calendar".localized, selection: Binding(
                    get: { self.item.plannedDate ?? Date() },
                    set: { newDate in
                        self.item.plannedDate = Calendar.current.startOfDay(for: newDate)
                    }
                ), displayedComponents: .date)
                .datePickerStyle(GraphicalDatePickerStyle())
                .labelsHidden()
                
                Button("confirm_button".localized) {
                    showCustomDatePicker = false
                }
            }
            .padding()
        }
    }
    
    private var noteBubble: some View {
        VStack(alignment: .leading, spacing: 8) {
            bubbleContent(backgroundColor: Color.gray.opacity(0.12), alignment: .leading)
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .leading)
    }

    private var taskBubble: some View {
        VStack(alignment: .trailing, spacing: 4) {
            bubbleContent(backgroundColor: Color.green.opacity(0.2), alignment: .trailing)
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .trailing)
    }

    private var incomeBubble: some View {
        VStack(alignment: .trailing, spacing: 4) {
            bubbleContent(backgroundColor: Color.green.opacity(0.2), alignment: .trailing)
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .trailing)
    }

    private var expenseBubble: some View {
        VStack(alignment: .leading, spacing: 4) {
            bubbleContent(backgroundColor: Color.green.opacity(0.12), alignment: .leading)
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .leading)
    }

    private func bubbleContent(backgroundColor: Color, alignment: HorizontalAlignment) -> some View {
        VStack(alignment: alignment, spacing: 2) {
            if !item.text.isEmpty || item.amount != nil {
                HStack(spacing: 4) {
                    // 添加收藏标记
                    if item.isFavorite {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                            .font(.caption2)
                    }
                    
                    if item.type == .task {
                        Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
                            .gesture(
                                LongPressGesture(minimumDuration: 0.5)
                                    .onEnded { _ in
                                        togglePomodoro()
                                    }
                            )
                            .simultaneousGesture(
                                TapGesture()
                                    .onEnded {
                                        toggleTaskCompletion()
                                    }
                            )
                            .onTapGesture {
                                // 空实现，阻止事件传递
                            }
                            .allowsHitTesting(true)
                    } else if item.type == .expense {
                        // Image(systemName: item.amount ?? 0 >= 0 ? "arrow.down.circle.fill" : "arrow.up.circle.fill")
                            // .foregroundColor(item.amount ?? 0 >= 0 ? .green.opacity(0.8) : .red.opacity(0.8))
                    }
                    
                    if !item.text.isEmpty {
                        Text(item.text)
                            .font(.system(size: bubbleFontSize))
                            .foregroundColor(.primary)
                            .fixedSize(horizontal: false, vertical: true)
                            .lineLimit(nil)
                            .lineSpacing(2)
                            
                            .tracking(0.2)
                    }

                    if let amount = item.amount {
                        HStack(spacing: 4) {
                            Text("\(amount >= 0 ? "+ " : "- ")\("currency_symbol".localized)\(String(format: "%.2f", abs(amount)))")
                                .font(.system(size: 14))
                                .fontWeight(.medium)
                                .foregroundColor(amount >= 0 ? .red : .green)
                            
                            if !item.category.isEmpty {
                                Text(item.category)
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                                    .padding(.vertical, 2)
                                    .padding(.horizontal, 4)
                                    .background(Color.primary.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                        // .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        // .background(amount >= 0 ? .red.opacity(0.15) : .green.opacity(0.1))
                        .cornerRadius(4)
                    }

                    

                    if let projectName = item.project?.name, isAllView {
                        Text(projectName)
                            .font(.caption2)
                            .foregroundColor(.primary.opacity(0.5))
                            .padding(.vertical, 2)
                            .padding(.horizontal, 4)
                            .background(Color.primary.opacity(0.1))
                            .cornerRadius(4)
                    }

                    if !item.comments.isEmpty {
                        commentCountBadge
                    }

                    if item.type == .task {
                        pomodoroIndicator
                    }

                    if item.type == .task && pomodoroManager.activePomodoro == item {
                        HStack {
                            Text(timeString(from: pomodoroManager.remainingTime))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .frame(width: 40)
                            
                            Button(action: {
                                isFullScreen = true
                            }) {
                                Image(systemName: "arrow.up.left.and.arrow.down.right")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }


                }
                
                
                .padding(12)
                .background(
                    ZStack(alignment: .leading) {
                        RoundedRectangle(cornerRadius: 12)
                            .fill(getBubbleBackgroundColor())
                            .animation(.easeInOut(duration: 0.2), value: isCompleted)
                        if item.type == .task && pomodoroManager.activePomodoro == item {
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.green.opacity(0.3))
                                .scaleEffect(x: pomodoroManager.progress, y: 1, anchor: .leading)
                                .animation(.linear(duration: 0.3), value: pomodoroManager.progress)
                        }
                    }
                )
                .clipShape(RoundedRectangle(cornerRadius: 12))
                .contentShape(RoundedRectangle(cornerRadius: 12))
                .onTapGesture {
                    onEdit()
                }
                .contextMenu(menuItems: {
                    bubbleContextMenu
                }, preview: {
                    VStack {
                        Text(item.text)
                            .font(.system(size: bubbleFontSize))
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(getBubbleBackgroundColor())
                            )
                    }
                    // .padding(8)
                })
                .pressEffect()
                .scaleEffect(pomodoroManager.activePomodoro == item ? 1.02 : 1)
                .shadow(
                    color: pomodoroManager.activePomodoro == item ? 
                        Color.green.opacity(0.3) : Color.clear,
                    radius: 8,
                    x: 0,
                    y: 2
                )
                .animation(.spring(
                    response: 0.3,
                    dampingFraction: 0.7,
                    blendDuration: 0
                ), value: pomodoroManager.activePomodoro == item)
            }
            
            if !item.imageData.isEmpty {
                imageContent(alignment: alignment)
                    .padding(.top, 2)
            }
            
            if !item.text.isEmpty || !item.imageData.isEmpty {
                HStack {
                    if alignment == .trailing {
                        Spacer()
                    }
                    
                    
                    if alignment == .leading {
                        Spacer()
                    }
                }
            }
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8) // 确保内容不会超出容器宽度
        .animation(.easeInOut(duration: 0.3), value: item.imageData)
        .fullScreenCover(isPresented: $isFullScreen) {
            FullScreenPomodoroView(
                taskTitle: item.text,
                onClose: { isFullScreen = false },
                totalPomodoros: item.pomodoroCount ?? 1 // 使用 pomodoroCount，如果为 nil 则默认为 1
            )
        }
    }

    private func imageContent(alignment: HorizontalAlignment) -> some View {
        GeometryReader { geometry in
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 8) {
                    ForEach(item.imageData.indices, id: \.self) { index in
                        if let uiImage = UIImage(data: item.imageData[index]) {
                            Image(uiImage: uiImage)
                                .resizable()
                                .scaledToFit()
                                .frame(height: 120)
                                .cornerRadius(8)
                                .onTapGesture {
                                    selectedImageIndex = index
                                    showImageViewer = true
                                }
                        }
                    }
                }
                .frame(minWidth: geometry.size.width, alignment: alignment == .trailing ? .trailing : .leading)
            }
        }
        .frame(height: 120)
        .cornerRadius(8)
        .fullScreenCover(isPresented: $showImageViewer) {
            ImageViewerView(chatItem: item, initialIndex: selectedImageIndex)
        }
    }

    private var tagView: some View {
        Group {
            if !item.tags.isEmpty {
                Text(item.tags.map { "#" + $0 }.joined(separator: " "))
                    .font(.caption2)
                    .foregroundColor(.secondary)
                    .padding(.top, 0)
                    .padding(.bottom, 10)
                    .padding(.horizontal, 4)
                    // .background(Color.gray.opacity(0.08))
                    .cornerRadius(4)
            }
        }
    }

    // 添加一个新的属性来断是否在"全"视图中
    @Environment(\.isAllView) private var isAllView
    
    func toggleTaskCompletion() {
        isCompleted.toggle()
        item.completed = isCompleted
    }

    private func togglePomodoro() {
        if pomodoroManager.activePomodoro == item {
            pomodoroManager.stopPomodoro()
        } else {
            pomodoroManager.startPomodoro(for: item)
        }
    }

    private func timeString(from seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    private var trashView: some View {
        VStack {
            Spacer()
            HStack {
                Spacer()
                Image(systemName: isOverTrash ? "trash.fill" : "trash")
                    .font(.largeTitle)
                    .foregroundColor(isOverTrash ? .red : .gray)
                Spacer()
            }
            .frame(height: 100)
            .background(Color.secondary.opacity(0.2))
        }
    }

    // 修改方法：格式化时间戳显示
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
                    formatter.dateFormat = "year_month_day_time_format".localized
        return formatter.string(from: date)
    }

    private var commentCountBadge: some View {
        Group {
            if !item.comments.isEmpty {
                Text("\(item.comments.count)")
                    .font(.caption2)
                    .fontWeight(.bold)
                    .foregroundColor(.secondary)
                    .frame(width: 16, height: 16) // 固定尺寸确保为圆形
                    .background(Color.black.opacity(0.1))
                    .clipShape(Circle()) // 改用 Circle 而不是 Capsule
            }
        }
    }

    // 新增数来确定气泡背景颜色
    private func getBubbleBackgroundColor() -> Color {
        let isDarkMode = colorScheme == .dark
        if let amount = item.amount {
            return amount >= 0 ? Color.gray.opacity(isDarkMode ? 0.2 : 0.07) : Color.gray.opacity(isDarkMode ? 0.2 : 0.07)  // 收入使用绿色背景，支出使用红色背景
        } else if item.type == .task {
            return isCompleted ? Color.green.opacity(isDarkMode ? 0.35 : 0.25) : Color.green.opacity(isDarkMode ? 0.25 : 0.15)
        } else { 
            return Color.gray.opacity(isDarkMode ? 0.2 : 0.1)  // 默认颜色（用于笔记等）
        }
    }

    private var bubbleContextMenu: some View {
        Group {
            // 添加收藏/取消收藏按钮
            Button(action: {
                item.isFavorite.toggle()
            }) {
                Label(item.isFavorite ? "unfavorite".localized : "favorite".localized,
                      systemImage: item.isFavorite ? "star.fill" : "star")
            }
            
            // 将切换类型的按钮移到最外层
            Button(action: {
                toggleItemType()
            }) {
                Label(item.type == .task ? "switch_to_note".localized : "switch_to_task".localized, 
                      systemImage: item.type == .task ? "note.text" : "checklist")
            }

            if item.type == .task {
                Section {
                    Button(action: {
                        toggleTaskCompletion()
                    }) {
                        Label(isCompleted ? "mark_incomplete".localized : "mark_complete".localized, systemImage: isCompleted ? "circle" : "checkmark.circle")
                    }
                
                                          Menu("set_plan".localized) {
                        Button("today".localized) {
                            setTaskDate(to: .today)
                        }
                        Button("tomorrow".localized) {
                            setTaskDate(to: .tomorrow)
                        }
                        Button("custom_date".localized) {
                            showCustomDatePicker = true
                        }
                        Divider()
                        Button("morning_9am".localized) {
                            setTaskTime(hour: 9, minute: 0)
                        }
                        Button("noon_12pm".localized) {
                            setTaskTime(hour: 12, minute: 0)
                        }
                        Button("evening_6pm".localized) {
                            setTaskTime(hour: 18, minute: 0)
                        }
                        Button("custom_time".localized) {
                            showCustomTimePicker = true
                        }
                        
                        if item.plannedDate != nil || item.plannedTime != nil {
                            Divider()
                            Button("cancel_plan".localized, role: .destructive) {
                                item.plannedDate = nil
                                item.plannedTime = nil
                            }
                        }
                    }

                }
                    
                Section {
                    Button(action: {
                        togglePomodoro()
                    }) {
                        // 修改停止番茄钟的样式
                        Label(pomodoroManager.activePomodoro == item ? "stop_pomodoro".localized : "start_pomodoro".localized, 
                              systemImage: "timer")
                    }
                    .foregroundColor(pomodoroManager.activePomodoro == item ? .red : nil)
                
                    Menu("set_pomodoro_count".localized) {
                        ForEach([1, 2, 3, 4], id: \.self) { count in
                            Button(String(format: "count_format".localized, count)) {
                                item.pomodoroCount = count
                            }
                        }
                        Button("custom_option".localized) {
                            tempPomodoroCount = item.pomodoroCount ?? 1
                            showCustomPomodoroCount = true
                        }
                    }
                }
                
            }
            

            
            Section {
                Button(action: {
                    UIPasteboard.general.string = item.text
                }) {
                    Label("copy_content".localized, systemImage: "doc.on.doc")
                }
                
                Button(action: {
                    // 实现编辑功能
                }) {
                    Label("edit".localized, systemImage: "pencil")
                }
                
                Button(role: .destructive, action: {
                    onDelete()
                }) {
                    Label("delete".localized, systemImage: "trash")
                }
            }
        }
        .sheet(isPresented: $showCustomPomodoroCount) {
            VStack {
                Text("set_pomodoro_count".localized)
                    .font(.headline)
                    .padding()
                
                PomodoroStepperView(count: $tempPomodoroCount)
                    .padding()
                
                HStack {
                    Button("cancel".localized) {
                        showCustomPomodoroCount = false
                    }
                    
                    Button("confirm".localized) {
                        item.pomodoroCount = tempPomodoroCount
                        showCustomPomodoroCount = false
                    }
                }
                .padding()
            }
        }
    }

    private func toggleItemType() {
        item.type = item.type == .task ? .note : .task
    }

    private func setTaskDate(to date: TaskDate) {
        switch date {
        case .today:
            item.plannedDate = Calendar.current.startOfDay(for: Date())
        case .tomorrow:
            item.plannedDate = Calendar.current.date(byAdding: .day, value: 1, to: Calendar.current.startOfDay(for: Date()))
        }
    }

    private func setTaskTime(hour: Int, minute: Int) {
        let calendar = Calendar.current
        let now = Date()
        let targetDate = item.plannedDate ?? now
        
        if let newTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: targetDate) {
            item.plannedTime = newTime
            if item.plannedDate == nil {
                item.plannedDate = calendar.startOfDay(for: now)
            }
        }
    }

    private enum TaskDate {
        case today
        case tomorrow
    }

    private var dateTimeView: some View {
        HStack {
            if let plannedDate = item.plannedDate {
                Text(plannedDate, style: .date)
                if let plannedTime = item.plannedTime {
                    Text(plannedTime, style: .time)
                }
            }
        }
        .font(.caption)
        .foregroundColor(.secondary)
    }

    private var pomodoroIndicator: some View {
        Group {
            if item.completedPomodoros > 0 || (item.pomodoroCount ?? 0) > 0 {
                HStack(spacing: 2) {
                    Image(systemName: "gauge.with.needle")
                        .foregroundColor(.secondary)
                        .font(.system(size: 8))
                    if let planned = item.pomodoroCount {
                        Text("\(item.completedPomodoros)/\(planned)")
                            .foregroundColor(.secondary)
                            .font(.system(size: 8))
                    } else {
                        Text("\(item.completedPomodoros)")
                            .foregroundColor(.secondary)
                            .font(.system(size: 8))
                    }
                }
            }
        }
    }

    // 添加一个计算属性来控制是否显示标签
    private var shouldShowTags: Bool {
        // 根据实际需求设置显示条件
        // 例如：只在计划视图中显示标签
        return false // 默认不在气泡下方显示标签
    }
}


// 添加一个扩展来获取对立的对齐方式
extension HorizontalAlignment {
    var opposite: HorizontalAlignment {
        switch self {
        case .leading:
            return .trailing
        case .trailing:
            return .leading
        default:
            return self
        }
    }
}

// 添加一个新的 ViewModifier 来处理按压效果
struct PressEffectViewModifier: ViewModifier {
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.98 : 1)
            .opacity(isPressed ? 0.9 : 1)
            .animation(.interpolatingSpring(
                mass: 1.0,
                stiffness: 100,
                damping: 10,
                initialVelocity: 0
            ), value: isPressed)
            .onLongPressGesture(minimumDuration: .infinity, maximumDistance: .infinity,
                pressing: { pressing in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = pressing
                    }
                },
                perform: { }
            )
    }
}

// 添加 View 扩展
extension View {
    func pressEffect() -> some View {
        modifier(PressEffectViewModifier())
    }
}






















