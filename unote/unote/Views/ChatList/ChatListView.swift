import SwiftUI
import Combine

// 优化的 ChatListView对状态进行精简
struct ChatListView: View {
    @Binding var items: [ChatItem]
    @State private var editingItem: ChatItem?
    @EnvironmentObject var pomodoroManager: PomodoroManager
    var onDelete: (ChatItem) -> Void
    var onLoadMore: (() -> Void)?
    @Binding var isViewingAllData: Bool
    let selectedDate: Date
    let isPlannedView: Bool
    let scrollToItem: ChatItem?
    let isLoading: Bool
    let hasMoreItems: Bool
    
    // 精简状态管理
    @State private var visibleTimestamps: Set<Date> = []
    @State private var isFirstLoad = true
    @State private var shouldScrollWithoutAnimation = true
    @State private var cachedGroupedItems: [(Date, [ChatItem])] = []
    @State private var lastItemsHash: Int = 0
    @State private var isInitialPositioned = false
    
    // 键盘相关状态
    @State private var keyboardHeight: CGFloat = 0
    @State private var isKeyboardVisible = false

    init(items: Binding<[ChatItem]>, 
         onDelete: @escaping (ChatItem) -> Void,
         onLoadMore: (() -> Void)? = nil,
         isViewingAllData: Binding<Bool>,
         selectedDate: Date,
         isPlannedView: Bool = false,
         scrollToItem: ChatItem? = nil,
         isLoading: Bool = false,
         hasMoreItems: Bool = true) {
        self._items = items
        self.onDelete = onDelete
        self.onLoadMore = onLoadMore
        self._isViewingAllData = isViewingAllData
        self.selectedDate = selectedDate
        self.isPlannedView = isPlannedView
        self.scrollToItem = scrollToItem
        self.isLoading = isLoading
        self.hasMoreItems = hasMoreItems
    }

    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 4) {
                    // 顶部加载指示器和感应区域
                    if hasMoreItems {
                        VStack {
                            if isLoading {
                                HStack {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                    Text("加载中...")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .frame(maxWidth: .infinity)
                                .padding(.vertical, 12)
                            } else {
                                // 添加一个顶部感应区域来触发加载更多历史
                                Rectangle()
                                    .fill(Color.clear)
                                    .frame(height: 50)
                                    .onAppear {
                                        print("顶部感应区域出现 - 触发加载更多历史")
                                        onLoadMore?()
                                    }
                            }
                        }
                    }
                    
                    ForEach(optimizedGroupedItems, id: \.0) { (timestamp, groupItems) in
                        TimestampHeaderView(timestamp: timestamp) {
                            visibleTimestamps.insert(timestamp)
                            updateCurrentContentDate()
                        } onDisappear: {
                            visibleTimestamps.remove(timestamp)
                            updateCurrentContentDate()
                        }
                        
                        ForEach(groupItems) { item in
                            OptimizedChatItemView(
                                item: item,
                                onEdit: { editingItem = item },
                                onDelete: { onDelete(item) }
                            )
                            .onAppear {
                                // 当滚动到最早的几个item时就开始加载历史消息  
                                let isNearTop = item == groupItems.first && 
                                               optimizedGroupedItems.first?.0 == timestamp
                                let shouldLoad = isNearTop && hasMoreItems && !isLoading
                                
                                if shouldLoad {
                                    print("触发加载更多历史: item=\(item.text.prefix(20)), hasMore=\(hasMoreItems), loading=\(isLoading)")
                                    onLoadMore?()
                                }
                            }
                        }
                    }
                    
                    // 添加底部间距，为键盘预留空间
                    Spacer()
                        .frame(height: max(0, keyboardHeight))
                        .animation(.easeInOut(duration: 0.25), value: keyboardHeight)
                }
                .clipShape(Rectangle())
            }
            .clipShape(Rectangle())
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillShowNotification)) { notification in
                handleKeyboardShow(notification, scrollProxy: proxy)
            }
            .onReceive(NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification)) { _ in
                handleKeyboardHide()
            }
            .onAppear {
                if let item = scrollToItem {
                    withAnimation {
                        proxy.scrollTo(item.id, anchor: .center)
                    }
                }
            }
            .onChange(of: scrollToItem) { _, newItem in
                if let item = newItem {
                    withAnimation {
                        proxy.scrollTo(item.id, anchor: .center)
                    }
                }
            }
            .onChange(of: items) { oldItems, newItems in
                // 如果是首次加载，立即定位到底部（无动画）
                if oldItems.isEmpty && !newItems.isEmpty && isFirstLoad {
                    if let lastItem = newItems.last {
                        // 使用异步确保视图完全加载
                        DispatchQueue.main.async {
                            proxy.scrollTo(lastItem.id, anchor: .bottom)
                        }
                    }
                    isFirstLoad = false
                } else if !oldItems.isEmpty && !newItems.isEmpty {
                    // 只有在添加新消息时才滚动（有动画）
                    if let lastItem = newItems.last, lastItem.id != oldItems.last?.id {
                        withAnimation(.easeOut) {
                            proxy.scrollTo(lastItem.id, anchor: .bottom)
                        }
                    }
                }
            }
            .onReceive(pomodoroManager.scrollToActivePomodoro) { _ in
                if let activeItem = pomodoroManager.activePomodoro {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        proxy.scrollTo(activeItem.id, anchor: .center)
                    }
                }
            }
        }
        .sheet(item: $editingItem) { item in
            EditChatItemView(
                item: item,
                onSave: { updatedItem in
                    if let index = items.firstIndex(where: { $0.id == updatedItem.id }) {
                        items[index] = updatedItem
                    }
                    editingItem = nil
                },
                onDelete: {
                    onDelete(item)
                    editingItem = nil
                }
            )
        }
    }
    
    // 优化的分组项目计算 - 使用后台线程
    private var optimizedGroupedItems: [(Date, [ChatItem])] {
        let itemsHash = items.hashValue
        
        // 检查缓存是否有效
        if itemsHash == lastItemsHash && !cachedGroupedItems.isEmpty {
            return cachedGroupedItems
        }
        
        // 如果缓存无效，直接计算并缓存（避免异步延迟）
        if cachedGroupedItems.isEmpty || itemsHash != lastItemsHash {
            let startTime = CFAbsoluteTimeGetCurrent()
            cachedGroupedItems = computeGroupedItems(items, selectedDate: selectedDate)
            lastItemsHash = itemsHash
            let endTime = CFAbsoluteTimeGetCurrent()
            print("🔍 [性能调试] 分组计算耗时: \(String(format: "%.2f", (endTime - startTime) * 1000))ms, 条目数: \(items.count)")
        }
        
        return cachedGroupedItems
    }
    
    private func computeGroupedItems(_ items: [ChatItem], selectedDate: Date) -> [(Date, [ChatItem])] {
        let calendar = Calendar.current
        let components: Set<Calendar.Component> = [.year, .month, .day, .hour]
        
        let groupingBlock: (ChatItem) -> Date = {
            let timestamp = isPlannedView ? ($0.plannedDate ?? $0.effectiveTimestamp) : $0.effectiveTimestamp
            let dateToGroup = calendar.date(from: calendar.dateComponents(components, from: timestamp)) ?? timestamp
            
            if !isViewingAllData {
                let selectedComponents = calendar.dateComponents([.year, .month, .day], from: selectedDate)
                let timeComponents = calendar.dateComponents([.hour], from: dateToGroup)
                
                var combinedComponents = DateComponents()
                combinedComponents.year = selectedComponents.year
                combinedComponents.month = selectedComponents.month
                combinedComponents.day = selectedComponents.day
                combinedComponents.hour = timeComponents.hour
                
                return calendar.date(from: combinedComponents) ?? dateToGroup
            }
            
            return dateToGroup
        }
        
        return Dictionary(grouping: items, by: groupingBlock)
            .map { (date, groupItems) in
                (groupItems.first?.effectiveTimestamp ?? date, groupItems.sorted {
                    let date1 = isPlannedView ? ($0.plannedDate ?? $0.effectiveTimestamp) : $0.effectiveTimestamp
                    let date2 = isPlannedView ? ($1.plannedDate ?? $1.effectiveTimestamp) : $1.effectiveTimestamp
                    return date1 < date2
                })
            }
            .sorted { $0.0 < $1.0 }
    }
    
    // 异步计算分组项目
    private func computeGroupedItemsAsync(_ items: [ChatItem], selectedDate: Date) async -> [(Date, [ChatItem])] {
        return await Task.detached(priority: .userInitiated) {
            self.computeGroupedItems(items, selectedDate: selectedDate)
        }.value
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let itemDate = calendar.startOfDay(for: date)
        
        if calendar.isDate(itemDate, inSameDayAs: today) {
            formatter.dateFormat = "HH:mm"
        } else {
                            formatter.dateFormat = "month_day_time_format".localized
        }
        
        return formatter.string(from: date)
    }
    
    private func updateCurrentContentDate() {
        if let mostRecentDate = visibleTimestamps.max() {
            NotificationCenter.default.post(
                name: NSNotification.Name("UpdateContentDate"),
                object: mostRecentDate
            )
        }
    }
    
    // MARK: - 键盘处理
    private func handleKeyboardShow(_ notification: Notification, scrollProxy: ScrollViewProxy) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else {
            return
        }
        
        let keyboardAnimationDuration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double ?? 0.25
        
        withAnimation(.easeInOut(duration: keyboardAnimationDuration)) {
            keyboardHeight = keyboardFrame.height
            isKeyboardVisible = true
        }
        
        // 延迟滚动到最新消息，确保键盘动画完成
        DispatchQueue.main.asyncAfter(deadline: .now() + keyboardAnimationDuration) {
            if let latestItem = items.last {
                withAnimation(.easeInOut(duration: 0.3)) {
                    scrollProxy.scrollTo(latestItem.id, anchor: .bottom)
                }
            }
        }
    }
    
    private func handleKeyboardHide() {
        withAnimation(.easeInOut(duration: 0.25)) {
            keyboardHeight = 0
            isKeyboardVisible = false
        }
    }
}

struct ContentSizePreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

struct ScrollViewSizePreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

struct ScrollOffsetPreferenceKey: PreferenceKey {
    static var defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = nextValue()
    }
}

