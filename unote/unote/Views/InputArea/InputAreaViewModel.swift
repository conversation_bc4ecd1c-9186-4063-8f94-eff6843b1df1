import SwiftUI
import PhotosUI
import Combine
import SwiftData
import AVFoundation

// 将大型 ViewModel 拆分为更小的状态管理单元
@Observable
final class InputAreaFormState {
    var inputText = ""
    var inputAmount = ""
    var selectedType: ItemType = .note
    var pomodoroCount = 0
    var tags: [String] = []
    var selectedImages: [UIImage] = []
    var selectedProject: Project?
    var selectedCategory = ""
    var transactionType: TransactionType = .expense
    var tagInput = ""
    var isSending = false
    
    var canSend: Bool {
        let hasAmount = !inputAmount.isEmpty
        let hasImages = !selectedImages.isEmpty
        return hasAmount || hasImages || !inputText.isEmpty
    }
    
    var placeholderText: String {
        switch selectedType {
        case .note:
            return "add_note_placeholder".localized
        case .task:
            return "add_task_placeholder".localized
        default:
            return "add_note_placeholder".localized
        }
    }
    
    func reset() {
        inputText = ""
        inputAmount = ""
        selectedImages.removeAll()
        pomodoroCount = 0
        tags.removeAll()
        selectedCategory = ""
        transactionType = .expense
        isSending = false
    }
}

@Observable
final class InputAreaUIState {
    var isInputFocused = false
    var isAmountInputFocused = false
    var isImagePickerPresented = false
    var isPhotoLibraryPresented = false
    var isCameraPresented = false
    var isTagsExpanded = false
    var showProjectList = false
    var showProjectSelectionSheet = false
    var showProjectMenu = false
    var showAmountInput = false
    var showCategoryPicker = false
    var showTagSuggestions = false
    var showTagManagement = false
    var showProjectSheet = false
    var keyboardIsVisible = false
}

@Observable
final class InputAreaViewModel {
    // 状态管理分离
    var formState = InputAreaFormState()
    var uiState = InputAreaUIState()
    
    // 数据状态
    var recentTags: [String] = []
    var categories: [String] = []
    var currentProjectFilter = ""
    var selectedProjectName: String?
    var projectSearchText = ""
    
    // Dependencies
    private var keyboardSubscription: AnyCancellable?
    let tagManager = TagManager()
    
    // MARK: - Initialization
    
    init() {
        setupKeyboardObserver()
        loadDefaultTags()
    }
    
    deinit {
        keyboardSubscription?.cancel()
    }
    
    // MARK: - Public Methods
    
    func onAppear(with currentProject: Project?) {
        if selectedProject == nil, let project = currentProject {
            selectedProject = project
        }
        loadCategories()
        loadRecentTags()
    }
    
    func handleInputChange(_ newValue: String) {
        formState.inputText = newValue
        
        if let lastChar = newValue.last, lastChar == "#" {
            formState.inputText = String(newValue.dropLast())
            uiState.showTagManagement = true
            formState.tagInput = ""
        } else if let lastChar = newValue.last, lastChar == "@" {
            uiState.showProjectSheet = true
            projectSearchText = ""
        }
    }
    
    func selectProject(_ project: Project) {
        formState.selectedProject = project
        selectedProjectName = project.name
        formState.inputText = formState.inputText.replacingOccurrences(of: "@\\S*", with: "", options: .regularExpression).trimmingCharacters(in: .whitespacesAndNewlines)
        uiState.isInputFocused = true
        uiState.showProjectSheet = false
    }
    
    func toggleTag(_ tag: String) {
        if formState.tags.contains(tag) {
            formState.tags.removeAll { $0 == tag }
        } else {
            formState.tags.append(tag)
            tagManager.addTag(tag)
        }
    }
    
    func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty else { return }
        
        if let hashIndex = formState.inputText.lastIndex(of: "#") {
            formState.inputText = String(formState.inputText[..<hashIndex])
        }
        
        withAnimation {
            if !formState.tags.contains(trimmedTag) {
                formState.tags.append(trimmedTag)
                tagManager.addTag(trimmedTag)
            }
        }
        
        uiState.isInputFocused = true
    }
    
    func checkAndRequestCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            uiState.isCameraPresented = true
        case .notDetermined:
            AVCaptureDevice.requestAccess(for: .video) { granted in
                DispatchQueue.main.async {
                    if granted {
                        self.uiState.isCameraPresented = true
                    }
                }
            }
        case .denied, .restricted:
            if let url = URL(string: UIApplication.openSettingsURLString) {
                UIApplication.shared.open(url)
            }
        @unknown default:
            break
        }
    }
    
    func updateSelectedType(for filter: ItemFilter) {
        switch filter {
        case .tasks:
            formState.selectedType = .task
        case .notes:
            formState.selectedType = .note
        case .all:
            break
        }
    }
    
    func resetInputState() {
        formState.reset()
        selectedProjectName = nil
        uiState.showAmountInput = false
    }
    
    func removeImage(at index: Int) {
        formState.selectedImages.remove(at: index)
    }
    
    // MARK: - Computed Properties
    
    // 代理属性优化性能
    var placeholderText: String {
        formState.placeholderText
    }
    
    var canSend: Bool {
        let hasAmount = uiState.showAmountInput && !formState.inputAmount.isEmpty
        let hasImages = !formState.selectedImages.isEmpty
        return hasAmount || hasImages || !formState.inputText.isEmpty
    }
    
    // 代理属性用于兼容性
    var inputText: String {
        get { formState.inputText }
        set { formState.inputText = newValue }
    }
    
    var inputAmount: String {
        get { formState.inputAmount }
        set { formState.inputAmount = newValue }
    }
    
    var selectedType: ItemType {
        get { formState.selectedType }
        set { formState.selectedType = newValue }
    }
    
    var pomodoroCount: Int {
        get { formState.pomodoroCount }
        set { formState.pomodoroCount = newValue }
    }
    
    var tags: [String] {
        get { formState.tags }
        set { formState.tags = newValue }
    }
    
    var selectedImages: [UIImage] {
        get { formState.selectedImages }
        set { formState.selectedImages = newValue }
    }
    
    var selectedProject: Project? {
        get { formState.selectedProject }
        set { formState.selectedProject = newValue }
    }
    
    var selectedCategory: String {
        get { formState.selectedCategory }
        set { formState.selectedCategory = newValue }
    }
    
    var transactionType: TransactionType {
        get { formState.transactionType }
        set { formState.transactionType = newValue }
    }
    
    var tagInput: String {
        get { formState.tagInput }
        set { formState.tagInput = newValue }
    }
    
    var isSending: Bool {
        get { formState.isSending }
        set { formState.isSending = newValue }
    }
    
    // UI 状态代理属性
    var isInputFocused: Bool {
        get { uiState.isInputFocused }
        set { uiState.isInputFocused = newValue }
    }
    
    var isAmountInputFocused: Bool {
        get { uiState.isAmountInputFocused }
        set { uiState.isAmountInputFocused = newValue }
    }
    
    var isImagePickerPresented: Bool {
        get { uiState.isImagePickerPresented }
        set { uiState.isImagePickerPresented = newValue }
    }
    
    var isPhotoLibraryPresented: Bool {
        get { uiState.isPhotoLibraryPresented }
        set { uiState.isPhotoLibraryPresented = newValue }
    }
    
    var isCameraPresented: Bool {
        get { uiState.isCameraPresented }
        set { uiState.isCameraPresented = newValue }
    }
    
    var showProjectSheet: Bool {
        get { uiState.showProjectSheet }
        set { uiState.showProjectSheet = newValue }
    }
    
    var showCategoryPicker: Bool {
        get { uiState.showCategoryPicker }
        set { uiState.showCategoryPicker = newValue }
    }
    
    var showTagManagement: Bool {
        get { uiState.showTagManagement }
        set { uiState.showTagManagement = newValue }
    }
    
    var showAmountInput: Bool {
        get { uiState.showAmountInput }
        set { uiState.showAmountInput = newValue }
    }
    
    var keyboardIsVisible: Bool {
        get { uiState.keyboardIsVisible }
        set { uiState.keyboardIsVisible = newValue }
    }
    
    // MARK: - Private Methods
    
    private func setupKeyboardObserver() {
        keyboardSubscription = NotificationCenter.default
            .publisher(for: UIResponder.keyboardWillShowNotification)
            .merge(with: NotificationCenter.default.publisher(for: UIResponder.keyboardWillHideNotification))
            .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main)
            .sink { [weak self] notification in
                guard let self = self else { return }
                
                let isShowing = notification.name == UIResponder.keyboardWillShowNotification
                
                // 优化：有选择地使用动画
                if isShowing {
                    self.uiState.keyboardIsVisible = true
                } else {
                    withAnimation(.easeInOut(duration: 0.2)) {
                        self.uiState.keyboardIsVisible = false
                        self.formState.selectedType = .note
                    }
                }
            }
    }
    
    func loadCategories() {
        let key = formState.transactionType == .income ? "incomeCategories" : "expenseCategories"
        categories = UserDefaults.standard.stringArray(forKey: key) ?? []
        if categories.isEmpty {
            categories = formState.transactionType == .income ?
            ["salary_category".localized, "investment_category".localized, "other_category".localized] :
            ["food_category".localized, "transport_category".localized, "shopping_category".localized, "other_category".localized]
            UserDefaults.standard.set(categories, forKey: key)
        }
    }
    
    private func loadRecentTags() {
        recentTags = UserDefaults.standard.stringArray(forKey: "recentTags") ?? []
    }
    
    private func loadDefaultTags() {
        recentTags = ["work".localized, "study".localized, "life".localized, "entertainment".localized]
    }
    
    private func saveRecentTags() {
        UserDefaults.standard.set(recentTags, forKey: "recentTags")
    }
    
    // MARK: - Data Operations
    
    func sendInput(with modelContext: ModelContext, currentProject: Project?, onSend: @escaping () -> Void) async {
        guard canSend else {
            print("no_content_to_send_debug".localized)
            return
        }
        
        guard !formState.isSending else {
            print("sending_in_progress_debug".localized)
            return
        }
        
        formState.isSending = true
        
        // 优化：立即更新UI，给用户即时反馈
        let inputTextSnapshot = formState.inputText
        let inputAmountSnapshot = formState.inputAmount
        let selectedTypeSnapshot = formState.selectedType
        let tagsSnapshot = formState.tags
        let pomodoroCountSnapshot = formState.pomodoroCount
        let selectedImagesSnapshot = formState.selectedImages
        let selectedCategorySnapshot = formState.selectedCategory
        let transactionTypeSnapshot = formState.transactionType
        let selectedProjectSnapshot = formState.selectedProject
        
        // 立即清理输入框，提供即时反馈
        await MainActor.run {
            resetInputState()
            onSend() // 立即触发界面更新
        }
        
        // 在后台进行数据处理
        let (amount, imageData) = await Task.detached(priority: .userInitiated) {
            let amount: Double?
            if !inputAmountSnapshot.isEmpty {
                if let inputAmountValue = Double(inputAmountSnapshot) {
                    amount = transactionTypeSnapshot == .income ? inputAmountValue : -inputAmountValue
                } else {
                    amount = nil
                }
            } else {
                amount = nil
            }
            
            // 优化图片压缩，使用后台线程并根据文件大小选择压缩比例
            let imageData = await withTaskGroup(of: Data.self, returning: [Data].self) { group in
                var results: [Data] = []
                
                for image in selectedImagesSnapshot {
                    group.addTask {
                        // 根据图片大小选择压缩比例
                        let imageSize = image.size.width * image.size.height
                        let compressionQuality: CGFloat = imageSize > 2000000 ? 0.5 : 0.7
                        return image.jpegData(compressionQuality: compressionQuality) ?? Data()
                    }
                }
                
                for await data in group {
                    results.append(data)
                }
                
                return results
            }
            
            return (amount, imageData)
        }.value
        
        // 在主线程上创建和保存ChatItem以确保线程安全
        do {
            try await MainActor.run {
                let newChatItem = ChatItem(
                    text: inputTextSnapshot,
                    amount: amount,
                    type: selectedTypeSnapshot,
                    tags: tagsSnapshot,
                    pomodoroCount: pomodoroCountSnapshot,
                    imageData: imageData,
                    category: selectedCategorySnapshot
                )
                
                newChatItem.project = selectedProjectSnapshot ?? currentProject
                
                try self.saveChatItemOptimized(newChatItem, with: modelContext)
                print("save_chat_item_success".localized)
            }
        } catch {
            print(String(format: "save_chat_item_error".localized, error.localizedDescription))
            // 可以在这里显示错误提示或重试逻辑
        }
        
        await MainActor.run {
            self.formState.isSending = false
        }
    }
    
    // 优化的数据保存方法
    private func saveChatItemOptimized(_ chatItem: ChatItem, with modelContext: ModelContext) throws {
        // 直接在主线程上执行 SwiftData 操作，避免 Sendable 问题
        modelContext.insert(chatItem)
        try modelContext.save()
    }
    
    // 保留原有方法作为备用
    @MainActor
    private func saveChatItem(_ chatItem: ChatItem, with modelContext: ModelContext) throws {
        modelContext.insert(chatItem)
        try modelContext.save()
        print("save_chat_item_success".localized)
    }
}

enum TransactionType {
    case income
    case expense
} 