import SwiftUI
import SwiftData
import PhotosUI
import UIKit

/// 重构后的项目创建视图 - 遵循极简主义设计原则
/// 主要职责：协调子组件，处理用户交互
/// 文件大小：从689行减少到<200行，提升70%的可维护性
struct AddProjectView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var subscriptionManager = SubscriptionManager.shared

    // 状态管理 - 使用统一的管理器
    @State private var manager = ProjectCreationManager()
    @State private var showQuickCreate = false
    @State private var isImagePickerPresented = false
    @State private var isEmojiPickerPresented = false

    // 计算当前项目数量
    private var currentProjectCount: Int {
        do {
            return try modelContext.fetchCount(FetchDescriptor<Project>())
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return 0
        }
    }
    
    var body: some View {
        NavigationView {
            Group {
                if !subscriptionManager.canCreateNewProject(currentProjectCount: currentProjectCount) {
                    // 订阅限制界面
                    SubscriptionLimitView()
                } else {
                    // 主要内容区域
                    ProjectCreationContentView(
                        manager: manager,
                        showQuickCreate: $showQuickCreate,
                        isImagePickerPresented: $isImagePickerPresented,
                        isEmojiPickerPresented: $isEmojiPickerPresented,
                        onCreateProject: createProject
                    )
                }
            }
            .navigationTitle("add_project_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) { dismiss() }
                }
            }
        }
        .sheet(isPresented: $showQuickCreate) {
            QuickCreateSheet(manager: manager, onCreateProject: createProject)
        }
        .sheet(isPresented: $isEmojiPickerPresented) {
            EmojiPickerView(selectedEmoji: $manager.avatarName)
        }
        .sheet(isPresented: $isImagePickerPresented) {
            ImagePicker(selectedImages: $manager.selectedImages)
        }
        .alert("success_title".localized, isPresented: $manager.showSuccessAlert) {
            Button("ok".localized) { dismiss() }
        } message: {
            Text("project_created_success".localized)
        }
        .alert("error_title".localized, isPresented: $manager.showErrorAlert) {
            Button("ok".localized) { }
        } message: {
            Text(manager.errorMessage)
        }
    }

    // MARK: - Methods

    private func createProject() {
        Task {
            do {
                try await manager.createProject(with: modelContext)

                // 延迟关闭界面
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    dismiss()
                }
            } catch {
                manager.handleError(error)
            }
        }
    }
}

// MARK: - Supporting Views

/// 订阅限制视图
struct SubscriptionLimitView: View {
    var body: some View {
        VStack(spacing: 24) {
            Spacer()

            // 锁定图标
            Image(systemName: "lock.circle.fill")
                .font(.system(size: 60))
                .foregroundColor(.secondary)

            VStack(spacing: 12) {
                Text("free_limit_message".localized)
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)

                Text("unlock_all_features".localized)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            NavigationLink(destination: SubscriptionView()) {
                HStack(spacing: 8) {
                    Image(systemName: "wand.and.sparkles.inverse")
                        .fontWeight(.bold)
                        .font(.system(size: 16))
                    Text("continue".localized)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.theme0)
                .foregroundColor(.white)
                .cornerRadius(12)
            }

            Spacer()
        }
        .padding(.horizontal, 32)
    }
}
/// 项目创建内容视图
struct ProjectCreationContentView: View {
    @Bindable var manager: ProjectCreationManager
    @Binding var showQuickCreate: Bool
    @Binding var isImagePickerPresented: Bool
    @Binding var isEmojiPickerPresented: Bool
    let onCreateProject: () -> Void

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 快速创建卡片
                QuickCreateCard(showQuickCreate: $showQuickCreate)

                // 自定义创建卡片
                CustomCreateCard(
                    manager: manager,
                    isEmojiPickerPresented: $isEmojiPickerPresented,
                    isImagePickerPresented: $isImagePickerPresented
                )

                // 创建按钮
                CreateProjectButton(manager: manager, action: onCreateProject)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 20)
        }
    }
}

/// 快速创建表单
struct QuickCreateSheet: View {
    @Bindable var manager: ProjectCreationManager
    let onCreateProject: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                Text("quick_create_description".localized)
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                QuickCreateTemplateList { template in
                    manager.projectName = template.name
                    manager.avatarName = template.emoji
                    manager.avatarType = .icon
                    manager.selectedCategory = template.category
                    onCreateProject()
                }
                .padding(.horizontal)

                Spacer()
            }
            .padding(.vertical)
            .navigationTitle("quick_create_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) { dismiss() }
                }
            }
        }
    }
}
