import SwiftUI
import SwiftData
import PhotosUI
import UIKit

struct AddProjectView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @State private var projectName = ""
    @State private var avatarType: AvatarType = .icon
    @State private var avatarName = "📁"
    @State private var selectedImages: [UIImage] = []
    @State private var isEmojiPickerPresented = false
    @State private var isImagePickerPresented = false
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var selectedCategory: ProjectCategory = .other
    @State private var showQuickCreate = false
    @State private var isCreating = false
    @State private var nameError: String? = nil
    @State private var showSuccessAlert = false
    @State private var showErrorAlert = false
    @State private var errorMessage = ""
    
    // 添加一个计算属性来获取当前项目数量
    private var currentProjectCount: Int {
        do {
            return try modelContext.fetchCount(FetchDescriptor<Project>())
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return 0
        }
    }
    
    var body: some View {
        NavigationView {
            Group {
                if !subscriptionManager.canCreateNewProject(currentProjectCount: currentProjectCount) {
                    // 订阅限制界面
                    VStack(spacing: 24) {
                        Spacer()
                        
                        // 锁定图标
                        Image(systemName: "lock.circle.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.secondary)
                        
                        VStack(spacing: 12) {
                            Text("free_limit_message".localized)
                                .font(.title2)
                                .fontWeight(.semibold)
                                .multilineTextAlignment(.center)
                            
                            Text("unlock_all_features".localized)
                                .font(.body)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                        
                        NavigationLink(destination: SubscriptionView()) {
                            HStack(spacing: 8) {
                                Image(systemName: "wand.and.sparkles.inverse")
                                    .fontWeight(.bold)
                                    .font(.system(size: 16))
                                Text("continue".localized)
                                    .fontWeight(.medium)
                            }
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.theme0)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                        }
                        
                        Spacer()
                    }
                    .padding(.horizontal, 32)
                } else {
                    // 主要内容区域
                    ScrollView {
                        VStack(spacing: 20) {
                            // 快速创建卡片
                            QuickCreateCard(showQuickCreate: $showQuickCreate)
                            
                            // 自定义创建卡片
                            CustomCreateCard(
                                projectName: $projectName,
                                avatarType: $avatarType,
                                avatarName: $avatarName,
                                selectedImages: $selectedImages,
                                selectedCategory: $selectedCategory,
                                isEmojiPickerPresented: $isEmojiPickerPresented,
                                isImagePickerPresented: $isImagePickerPresented,
                                nameError: $nameError,
                                isCreating: isCreating,
                                onSave: saveProject
                            )
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 20)
                    }
                    .navigationTitle("new_project_title".localized)
                    .navigationBarTitleDisplayMode(.large)
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("cancel".localized) { dismiss() }
                        }
                    }
                    .sheet(isPresented: $isEmojiPickerPresented) {
                        EmojiPicker(selectedEmoji: $avatarName)
                    }
                    .sheet(isPresented: $isImagePickerPresented) {
                        ImagePicker(images: $selectedImages)
                    }
                    .sheet(isPresented: $showQuickCreate) {
                        QuickProjectView(selectedCategory: $selectedCategory)
                    }
                    .overlay {
                        if showSuccessAlert {
                            SuccessOverlay()
                        }
                    }
                    .alert("error".localized, isPresented: $showErrorAlert) {
                        Button("confirm".localized, role: .cancel) { }
                    } message: {
                        Text(errorMessage)
                    }
                }
            }
        }
    }
    
    private func saveProject() {
        guard !isCreating else { return }
        guard nameError == nil else { return }
        
        isCreating = true
        
        // 检查项目名是否已存在
        let descriptor = FetchDescriptor<Project>(
            predicate: #Predicate<Project> { project in
                project.name == projectName
            }
        )
        
        do {
            let existingProjects = try modelContext.fetch(descriptor)
            if !existingProjects.isEmpty {
                throw ProjectError.nameExists
            }
            
            let newProject = Project(name: projectName, avatarType: avatarType, avatarName: avatarName)
            if avatarType == .image, let image = selectedImages.first {
                // 限制图片大小
                let maxSize: CGFloat = 1024
                let scale = min(maxSize/image.size.width, maxSize/image.size.height, 1.0)
                let newSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)
                
                let renderer = UIGraphicsImageRenderer(size: newSize)
                let compressedImage = renderer.image { context in
                    image.draw(in: CGRect(origin: .zero, size: newSize))
                }
                
                if let imageData = compressedImage.jpegData(compressionQuality: 0.8) {
                    newProject.avatarImageData = imageData
                }
            }
            modelContext.insert(newProject)
            try modelContext.save()
            
            // 显示成功提示
            withAnimation {
                showSuccessAlert = true
            }
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                dismiss()
            }
            
        } catch ProjectError.nameExists {
            nameError = "project_name_exists".localized
            isCreating = false
        } catch {
            // 显示错误提示
            errorMessage = String(format: "save_failed_error".localized, error.localizedDescription)
            showErrorAlert = true
            isCreating = false
        }
    }
}

// MARK: - 快速创建卡片
struct QuickCreateCard: View {
    @Binding var showQuickCreate: Bool
    
    var body: some View {
        Button(action: { showQuickCreate = true }) {
            HStack(spacing: 12) {
                // 左侧图标
                Image(systemName: "bolt.fill")
                    .font(.system(size: 20))
                    .fontWeight(.medium)
                    .foregroundColor(.yellow)
                    .frame(width: 48, height: 48)
                    .background(Color.yellow.opacity(0.1))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color.yellow.opacity(0.2), lineWidth: 0.50)
                    )
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("quick_create_title".localized)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(Color(.label))
                    
                    Text("quick_create_subtitle".localized)
                        .font(.system(size: 14))
                        .foregroundColor(Color(.secondaryLabel))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(.tertiaryLabel))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .inset(by: 0.50)
                    .stroke(Color(.separator), lineWidth: 0.50)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 自定义创建卡片
struct CustomCreateCard: View {
    @Binding var projectName: String
    @Binding var avatarType: AvatarType
    @Binding var avatarName: String
    @Binding var selectedImages: [UIImage]
    @Binding var selectedCategory: ProjectCategory
    @Binding var isEmojiPickerPresented: Bool
    @Binding var isImagePickerPresented: Bool
    @Binding var nameError: String?
    let isCreating: Bool
    let onSave: () -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // 标题
            Text("project_info_section".localized)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(.label))
            
            // 项目名称输入
            VStack(alignment: .leading, spacing: 8) {
                Text("project_name".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(.label))
                
                TextField("project_name_placeholder".localized, text: $projectName)
                    .textFieldStyle(CustomTextFieldStyle())
                    .onChange(of: projectName) { oldValue, newValue in
                        validateProjectName(newValue)
                    }
                
                if let error = nameError {
                    Text(error)
                        .font(.system(size: 12))
                        .foregroundColor(.red)
                }
            }
            
            // 项目分类选择
            VStack(alignment: .leading, spacing: 12) {
                Text("project_category_picker".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(.label))
                
                CategoryPicker(selectedCategory: $selectedCategory)
            }
            
            // 头像类型选择
            VStack(alignment: .leading, spacing: 12) {
                Text("avatar_type_picker".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(Color(.label))
                
                Picker("avatar_type_picker".localized, selection: $avatarType) {
                    Text("icon_option".localized).tag(AvatarType.icon)
                    Text("avatar_image".localized).tag(AvatarType.image)
                }
                .pickerStyle(SegmentedPickerStyle())
                
                // 头像内容
                if avatarType == .icon {
                    IconSelector(
                        avatarName: $avatarName,
                        isEmojiPickerPresented: $isEmojiPickerPresented
                    )
                } else {
                    ImageSelector(
                        selectedImages: $selectedImages,
                        isImagePickerPresented: $isImagePickerPresented
                    )
                }
            }
            
            // 保存按钮
            Button(action: onSave) {
                HStack {
                    if isCreating {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    } else {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 16, weight: .medium))
                    }
                    
                    Text(isCreating ? "creating_project".localized : "save".localized)
                        .font(.system(size: 16, weight: .semibold))
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(
                    projectName.isEmpty || isCreating || nameError != nil
                    ? Color.gray.opacity(0.3)
                    : Color.theme0
                )
                .foregroundColor(.white)
                .cornerRadius(12)
            }
            .disabled(projectName.isEmpty || isCreating || nameError != nil)
        }
        .padding(20)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .inset(by: 0.50)
                .stroke(Color(.separator), lineWidth: 0.50)
        )
    }
    
    private func validateProjectName(_ name: String) {
        if name.isEmpty {
            nameError = "project_name_empty".localized
        } else if name.count > 50 {
            nameError = "project_name_too_long".localized
        } else {
            nameError = nil
        }
    }
}

// MARK: - 自定义文本输入框样式
struct CustomTextFieldStyle: TextFieldStyle {
    @Environment(\.colorScheme) private var colorScheme
    
    // swiftlint:disable:next identifier_name
    func _body(configuration: TextField<Self._Label>) -> some View {
        configuration
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemGray6))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.50)
                    .stroke(Color(.separator), lineWidth: 0.50)
            )
    }
}

// MARK: - 分类选择器
struct CategoryPicker: View {
    @Binding var selectedCategory: ProjectCategory
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(ProjectCategory.allCases, id: \.self) { category in
                    CategoryChip(
                        category: category,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal, 4)
        }
    }
}

// MARK: - 分类芯片
struct CategoryChip: View {
    let category: ProjectCategory
    let isSelected: Bool
    let action: () -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 6) {
                Text(category.icon)
                    .font(.system(size: 16))
                
                Text(category.rawValue)
                    .font(.system(size: 14, weight: .medium))
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                isSelected ? Color.theme0 : Color(.systemGray6)
            )
            .foregroundColor(
                isSelected ? .white : Color(.label)
            )
            .cornerRadius(20)
            .overlay(
                RoundedRectangle(cornerRadius: 20)
                    .inset(by: 0.50)
                    .stroke(
                        isSelected ? Color.theme0 : Color(.separator),
                        lineWidth: 0.50
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 图标选择器
struct IconSelector: View {
    @Binding var avatarName: String
    @Binding var isEmojiPickerPresented: Bool
    
    var body: some View {
        Button(action: { isEmojiPickerPresented = true }) {
            HStack(spacing: 12) {
                // 当前选中的图标
                Text(avatarName)
                    .font(.system(size: 28))
                    .frame(width: 48, height: 48)
                    .background(Color(.systemGray6))
                    .cornerRadius(10)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("select_icon_button".localized)
                        .font(.system(size: 15, weight: .medium))
                        .foregroundColor(Color(.label))
                    
                    Text("tap_to_change".localized)
                        .font(.system(size: 12))
                        .foregroundColor(Color(.secondaryLabel))
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(Color(.tertiaryLabel))
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(Color(.systemBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .inset(by: 0.50)
                    .stroke(Color(.separator), lineWidth: 0.50)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 图片选择器
struct ImageSelector: View {
    @Binding var selectedImages: [UIImage]
    @Binding var isImagePickerPresented: Bool
    
    var body: some View {
        HStack {
            if let image = selectedImages.first {
                Image(uiImage: image)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 60, height: 60)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.separator), lineWidth: 0.50)
                    )
            } else {
                Image(systemName: "photo")
                    .font(.system(size: 24))
                    .foregroundColor(Color(.tertiaryLabel))
                    .frame(width: 60, height: 60)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.separator), lineWidth: 0.50)
                    )
            }
            
            Spacer()
            
            Button("select_image_button".localized) {
                isImagePickerPresented = true
            }
            .font(.system(size: 14, weight: .medium))
            .foregroundColor(Color.theme0)
        }
    }
}

// MARK: - 成功覆盖层
struct SuccessOverlay: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 48))
                .foregroundColor(.green)
            
            Text("project_created_success".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.green)
        }
        .padding(24)
        .background(.ultraThinMaterial)
        .cornerRadius(16)
        .transition(.scale.combined(with: .opacity))
    }
}

struct EmojiPicker: View {
    @Binding var selectedEmoji: String
    @Environment(\.dismiss) private var dismiss
    @State private var customEmoji = ""
    @State private var searchText = ""
    
    // 按类别组织的emoji
    let emojiCategories = [
        ("常用", ["📁", "📂", "🗂️", "📊", "📈", "📉", "📝", "✏️", "📌", "🏷️"]),
        ("时间", ["🗓️", "⏰", "📅", "⏱️", "⏲️", "🕐", "🕑", "🕒", "🕓", "🕔"]),
        ("工作", ["💼", "🏢", "🏫", "🏠", "📋", "📎", "📏", "📐", "🖊️", "🖋️"]),
        ("创意", ["🌟", "🎨", "🎵", "📚", "🎭", "🎪", "🎯", "🎲", "🎮", "🎸"]),
        ("科技", ["💻", "📱", "🔬", "🔧", "🛠️", "🚀", "⚡", "🔋", "💾", "📡"]),
        ("生活", ["🏠", "🏡", "🏘️", "🏚️", "🏗️", "🏭", "🏢", "🏬", "🏣", "🏤"])
    ]
    
    var filteredEmojis: [(String, [String])] {
        if searchText.isEmpty {
            return emojiCategories
        } else {
            return emojiCategories.compactMap { category, emojis in
                let filtered = emojis.filter { $0.contains(searchText) }
                return filtered.isEmpty ? nil : (category, filtered)
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(Color(.secondaryLabel))
                    
                    TextField("search_icons".localized, text: $searchText)
                        .textFieldStyle(PlainTextFieldStyle())
                    
                    if !searchText.isEmpty {
                        Button(action: { searchText = "" }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(Color(.secondaryLabel))
                        }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))
                .cornerRadius(10)
                .padding(.horizontal, 16)
                .padding(.top, 8)
                
                // 自定义输入区域
                if searchText.isEmpty {
                    VStack(spacing: 12) {
                        HStack {
                            Text("custom_icon_section".localized)
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(Color(.label))
                            Spacer()
                        }
                        .padding(.horizontal, 20)
                        .padding(.top, 16)
                        
                        HStack(spacing: 12) {
                            TextField("enter_emoji".localized, text: $customEmoji)
                                .textFieldStyle(CustomTextFieldStyle())
                                .font(.title2)
                            
                            Button(action: {
                                if !customEmoji.isEmpty {
                                    selectedEmoji = customEmoji
                                    dismiss()
                                }
                            }) {
                                Text("use_button".localized)
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 16)
                                    .padding(.vertical, 8)
                                    .background(customEmoji.isEmpty ? Color.gray : Color.theme0)
                                    .cornerRadius(8)
                            }
                            .disabled(customEmoji.isEmpty)
                        }
                        .padding(.horizontal, 20)
                    }
                }
                
                // Emoji网格
                ScrollView {
                    LazyVStack(spacing: 20) {
                        ForEach(filteredEmojis, id: \.0) { category, emojis in
                            VStack(alignment: .leading, spacing: 12) {
                                if !searchText.isEmpty {
                                    Text(category)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(Color(.label))
                                        .padding(.horizontal, 20)
                                }
                                
                                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 8), spacing: 8) {
                                    ForEach(emojis, id: \.self) { emoji in
                                        Button(action: {
                                            selectedEmoji = emoji
                                            dismiss()
                                        }) {
                                            Text(emoji)
                                                .font(.system(size: 24))
                                                .frame(width: 44, height: 44)
                                                .background(Color(.systemGray6))
                                                .cornerRadius(8)
                                        }
                                        .buttonStyle(PlainButtonStyle())
                                    }
                                }
                                .padding(.horizontal, 20)
                            }
                        }
                    }
                    .padding(.vertical, 16)
                }
            }
            .navigationTitle("select_icon_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) { dismiss() }
                }
            }
        }
    }
}

enum ProjectError: Error {
    case nameExists
}
