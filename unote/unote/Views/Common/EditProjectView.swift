import SwiftUI
import SwiftData

struct EditProjectView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    @Bindable var project: Project
    @State private var name: String
    @State private var avatarType: AvatarType
    @State private var avatarName: String
    @State private var isEmojiPickerPresented = false
    @State private var isImagePickerPresented = false
    @State private var selectedImages: [UIImage] = []
    @State private var showDeleteConfirmation = false
    @State private var customEmoji = ""
    @State private var selectedIconCategory: IconCategory = .faces
    
    enum IconCategory: String, CaseIterable {
        case faces = "faces_avatar"
        case nature = "nature_avatar"
        case objects = "objects_avatar"
        case symbols = "symbols_avatar"
        case custom = "custom_avatar"
        
        var localizedString: String {
            return self.rawValue.localized
        }
        
        var icons: [String] {
            switch self {
            case .faces: return ["😀", "😊", "🤔", "😎", "🤓", "😍"]
            case .nature: return ["🌲", "🌺", "🌙", "⭐️", "🌈", "🍀"]
            case .objects: return ["📱", "💻", "📚", "✏️", "📌", "🎮"]
            case .symbols: return ["❤️", "⭐️", "✨", "💫", "🔥", "✅"]
            case .custom: return []
            }
        }
    }
    
    init(project: Project) {
        self.project = project
        _name = State(initialValue: project.name)
        _avatarType = State(initialValue: project.avatarType)
        _avatarName = State(initialValue: project.avatarName)
    }
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("project_info_section".localized)) {
                    TextField("project_name".localized, text: $name)
                    
                    Picker("picker_avatar_type".localized, selection: $avatarType) {
                        Text("icon_option".localized).tag(AvatarType.icon)
                        Text("avatar_image".localized).tag(AvatarType.image)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    
                    if avatarType == .icon {
                        Section(header: Text("icon_selection_section".localized)) {
                            Picker("picker_icon_category".localized, selection: $selectedIconCategory) {
                                ForEach(IconCategory.allCases, id: \.self) { category in
                                    Text(category.localizedString).tag(category)
                                }
                            }
                            
                            if selectedIconCategory == .custom {
                                TextField("enter_custom_icon".localized, text: $customEmoji)
                            } else {
                                LazyVGrid(columns: [
                                    GridItem(.adaptive(minimum: 44))
                                ], spacing: 10) {
                                    ForEach(selectedIconCategory.icons, id: \.self) { icon in
                                        Text(icon)
                                            .font(.title)
                                            .onTapGesture {
                                                avatarName = icon
                                            }
                                            .background(avatarName == icon ? Color.blue.opacity(0.3) : Color.clear)
                                            .cornerRadius(8)
                                    }
                                }
                            }
                        }
                    } else {
                        HStack {
                            if let imageData = project.avatarImageData,
                               let uiImage = UIImage(data: imageData) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(height: 100)
                            } else if let firstImage = selectedImages.first {
                                Image(uiImage: firstImage)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(height: 100)
                            } else {
                                Text("no_image_selected".localized)
                            }
                            Spacer()
                            Button("select_image_button".localized) {
                                isImagePickerPresented = true
                            }
                        }
                    }
                }
                
                Section {
                    Button(role: .destructive) {
                        showDeleteConfirmation = true
                    } label: {
                        HStack {
                            Image(systemName: "trash")
                            Text("delete_project_title".localized)
                        }
                    }
                }
            }
            .navigationTitle("edit_project".localized)
            .navigationBarItems(
                            leading: Button("cancel".localized) { dismiss() },
            trailing: Button("save".localized) {
                    saveChanges()
                }
                .disabled(name.isEmpty)
            )
            .sheet(isPresented: $isEmojiPickerPresented) {
                EmojiPicker(selectedEmoji: $avatarName)
            }
            .sheet(isPresented: $isImagePickerPresented) {
                ImagePicker(images: $selectedImages)
            }
            .confirmationDialog(
                "confirm_delete_project_title".localized,
                isPresented: $showDeleteConfirmation,
                titleVisibility: .visible
            ) {
                Button("delete".localized, role: .destructive) {
                    deleteProject()
                }
                Button("cancel".localized, role: .cancel) { }
            } message: {
                Text("cannot_undo_operation".localized)
            }
        }
    }
    
    private func deleteProject() {
        // 先删除所有关联的 ChatItems
        if let chatItems = project.chatItems {
            for item in chatItems {
                modelContext.delete(item)
            }
        }
        
        // 然后删除项目本身
        modelContext.delete(project)
        
        // 添加短暂延迟确保删除操作完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            do {
                try modelContext.save()
                dismiss()
            } catch {
                print(String(format: "delete_project_failed".localized, error.localizedDescription))
            }
        }
    }
    
    private func saveChanges() {
        project.name = name
        project.avatarType = avatarType
        
        if avatarType == .icon {
            if selectedIconCategory == .custom {
                project.avatarName = customEmoji
            } else {
                project.avatarName = avatarName
            }
            project.avatarImageData = nil
        } else if let imageData = selectedImages.first?.jpegData(compressionQuality: 0.8) {
            project.avatarImageData = imageData
            project.avatarName = ""
        }
        
        project.updateLastUpdated()
        
        do {
            try modelContext.save()
            dismiss()
        } catch {
            print(String(format: "save_project_failed".localized, error.localizedDescription))
        }
    }
}