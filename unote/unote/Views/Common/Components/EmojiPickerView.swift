import SwiftUI

// MARK: - Emoji Picker View
// 表情选择器组件，遵循极简主义设计原则

struct EmojiPickerView: View {
    @Binding var selectedEmoji: String
    @Environment(\.dismiss) private var dismiss
    
    // 表情分类
    private let emojiCategories: [EmojiCategory] = [
        EmojiCategory(
            name: "objects".localized,
            emojis: ["📁", "📂", "🗂", "📋", "📊", "📈", "📉", "🗒", "🗓", "📅", "📆", "🗃", "🗄", "📇", "🗳", "🗞", "📰", "📑", "🔖", "🏷", "💼", "📁", "📂", "🗂", "📋"]
        ),
        EmojiCategory(
            name: "work".localized,
            emojis: ["💼", "🏢", "🏭", "🏗", "⚙️", "🔧", "🔨", "⚒", "🛠", "⛏", "🔩", "⚙️", "🖥", "💻", "⌨️", "🖱", "🖨", "📱", "☎️", "📞", "📟", "📠", "📺", "📻", "🎙"]
        ),
        EmojiCategory(
            name: "study".localized,
            emojis: ["📚", "📖", "📝", "✏️", "✒️", "🖊", "🖋", "🖌", "🖍", "📏", "📐", "📌", "📍", "📎", "🖇", "📁", "📂", "🗂", "📋", "📊", "📈", "📉", "🗒", "🗓", "📅"]
        ),
        EmojiCategory(
            name: "life".localized,
            emojis: ["🏠", "🏡", "🏘", "🏚", "🏗", "🏭", "🏢", "🏬", "🏣", "🏤", "🏥", "🏦", "🏨", "🏪", "🏫", "🏩", "💒", "🏛", "⛪️", "🕌", "🕍", "🕋", "⛩", "🛤", "🛣"]
        ),
        EmojiCategory(
            name: "health".localized,
            emojis: ["💪", "🏃‍♂️", "🏃‍♀️", "🚴‍♂️", "🚴‍♀️", "🏊‍♂️", "🏊‍♀️", "🧘‍♂️", "🧘‍♀️", "🏋️‍♂️", "🏋️‍♀️", "🤸‍♂️", "🤸‍♀️", "🏌️‍♂️", "🏌️‍♀️", "🏄‍♂️", "🏄‍♀️", "🚣‍♂️", "🚣‍♀️", "🏇", "⛷", "🏂", "🏊", "🏄", "🚣"]
        ),
        EmojiCategory(
            name: "hobby".localized,
            emojis: ["🎨", "🖌", "🖍", "🎭", "🎪", "🎨", "🎬", "🎤", "🎧", "🎼", "🎵", "🎶", "🎹", "🥁", "🎷", "🎺", "🎸", "🪕", "🎻", "🎲", "♠️", "♥️", "♦️", "♣️", "🃏"]
        ),
        EmojiCategory(
            name: "travel".localized,
            emojis: ["✈️", "🛫", "🛬", "🛩", "💺", "🚁", "🚟", "🚠", "🚡", "🛰", "🚀", "🛸", "🚂", "🚃", "🚄", "🚅", "🚆", "🚇", "🚈", "🚉", "🚊", "🚝", "🚞", "🚋", "🚌"]
        ),
        EmojiCategory(
            name: "nature".localized,
            emojis: ["🌱", "🌿", "☘️", "🍀", "🎍", "🎋", "🍃", "🍂", "🍁", "🍄", "🌾", "💐", "🌷", "🌹", "🥀", "🌺", "🌸", "🌼", "🌻", "🌞", "🌝", "🌛", "🌜", "🌚", "🌕"]
        )
    ]
    
    @State private var selectedCategory = 0
    @State private var searchText = ""
    
    var filteredEmojis: [String] {
        let categoryEmojis = emojiCategories[selectedCategory].emojis
        if searchText.isEmpty {
            return categoryEmojis
        } else {
            return categoryEmojis.filter { emoji in
                // 这里可以添加更复杂的搜索逻辑
                true
            }
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                SearchBar(text: $searchText)
                    .padding(.horizontal)
                    .padding(.top, 8)
                
                // 分类选择器
                CategorySelector(
                    categories: emojiCategories,
                    selectedCategory: $selectedCategory
                )
                .padding(.horizontal)
                .padding(.vertical, 8)
                
                // 表情网格
                EmojiGrid(
                    emojis: filteredEmojis,
                    selectedEmoji: $selectedEmoji,
                    onSelect: { emoji in
                        selectedEmoji = emoji
                        dismiss()
                    }
                )
            }
            .navigationTitle("select_emoji_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar(content: {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            })
        }
    }
}

// MARK: - Supporting Components

/// 搜索栏组件
struct SearchBar: View {
    @Binding var text: String
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("search_emoji_placeholder".localized, text: $text)
                .textFieldStyle(PlainTextFieldStyle())
            
            if !text.isEmpty {
                Button(action: { text = "" }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
    }
}

/// 分类选择器组件
struct CategorySelector: View {
    let categories: [EmojiCategory]
    @Binding var selectedCategory: Int
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(0..<categories.count, id: \.self) { index in
                    CategoryTab(
                        title: categories[index].name,
                        isSelected: selectedCategory == index
                    ) {
                        withAnimation(.easeInOut(duration: 0.2)) {
                            selectedCategory = index
                        }
                    }
                }
            }
            .padding(.horizontal)
        }
    }
}

/// 分类标签组件
struct CategoryTab: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(isSelected ? .white : .primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? 
                        LinearGradient(
                            gradient: Gradient(colors: [.blue, .purple]),
                            startPoint: .leading,
                            endPoint: .trailing
                        ) :
                        LinearGradient(
                            gradient: Gradient(colors: [Color(.systemGray6), Color(.systemGray6)]),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                )
                .cornerRadius(20)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 表情网格组件
struct EmojiGrid: View {
    let emojis: [String]
    @Binding var selectedEmoji: String
    let onSelect: (String) -> Void
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 8), spacing: 12) {
                ForEach(emojis, id: \.self) { emoji in
                    EmojiButton(
                        emoji: emoji,
                        isSelected: selectedEmoji == emoji,
                        onTap: { onSelect(emoji) }
                    )
                }
            }
            .padding(.horizontal)
            .padding(.vertical, 8)
        }
    }
}

/// 表情按钮组件
struct EmojiButton: View {
    let emoji: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(emoji)
                .font(.system(size: 28))
                .frame(width: 44, height: 44)
                .background(
                    isSelected ? 
                        Color.blue.opacity(0.2) : 
                        Color(.systemGray6)
                )
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(
                            isSelected ? Color.blue : Color.clear,
                            lineWidth: 2
                        )
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Data Models

/// 表情分类数据模型
struct EmojiCategory {
    let name: String
    let emojis: [String]
}

// MARK: - Preview
#Preview {
    EmojiPickerView(selectedEmoji: .constant("📁"))
}
