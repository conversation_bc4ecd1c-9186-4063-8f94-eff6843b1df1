import SwiftUI
import SwiftData
import UIKit

// MARK: - Project Creation Manager
// 项目创建业务逻辑管理器，遵循单一职责原则

@Observable
class ProjectCreationManager {
    // MARK: - State
    var projectName = ""
    var avatarType: AvatarType = .icon
    var avatarName = "📁"
    var selectedImages: [UIImage] = []
    var selectedCategory: ProjectCategory = .other
    var nameError: String? = nil
    var isCreating = false
    var showSuccessAlert = false
    var showErrorAlert = false
    var errorMessage = ""
    
    // MARK: - Validation
    var isValid: Bool {
        !projectName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty && nameError == nil
    }
    
    var canCreate: Bool {
        isValid && !isCreating
    }
    
    // MARK: - Methods
    func validateProjectName(with modelContext: ModelContext) {
        let trimmedName = projectName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard !trimmedName.isEmpty else {
            nameError = "project_name_empty".localized
            return
        }
        
        guard trimmedName.count <= 50 else {
            nameError = "project_name_too_long".localized
            return
        }
        
        // 检查名称是否已存在
        let descriptor = FetchDescriptor<Project>(
            predicate: #Predicate<Project> { project in
                project.name == trimmedName
            }
        )
        
        do {
            let existingProjects = try modelContext.fetch(descriptor)
            if !existingProjects.isEmpty {
                nameError = "project_name_exists".localized
            } else {
                nameError = nil
            }
        } catch {
            nameError = "validation_error".localized
        }
    }
    
    func createProject(with modelContext: ModelContext) async throws {
        guard canCreate else { return }
        
        isCreating = true
        defer { isCreating = false }
        
        let trimmedName = projectName.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // 再次验证名称
        validateProjectName(with: modelContext)
        guard nameError == nil else {
            throw ProjectCreationError.invalidName
        }
        
        let newProject = Project(
            name: trimmedName,
            avatarType: avatarType,
            avatarName: avatarName
        )
        
        // 处理头像图片
        if avatarType == .image, let image = selectedImages.first {
            if let compressedData = await compressImage(image) {
                newProject.avatarImageData = compressedData
            }
        }
        
        // 设置分类
        newProject.category = selectedCategory
        
        // 保存到数据库
        modelContext.insert(newProject)
        try modelContext.save()
        
        // 显示成功状态
        await MainActor.run {
            showSuccessAlert = true
        }
    }
    
    private func compressImage(_ image: UIImage) async -> Data? {
        return await Task.detached(priority: .userInitiated) {
            // 限制图片大小
            let maxSize: CGFloat = 1024
            let scale = min(maxSize/image.size.width, maxSize/image.size.height, 1.0)
            let newSize = CGSize(width: image.size.width * scale, height: image.size.height * scale)
            
            let renderer = UIGraphicsImageRenderer(size: newSize)
            let compressedImage = renderer.image { context in
                image.draw(in: CGRect(origin: .zero, size: newSize))
            }
            
            return compressedImage.jpegData(compressionQuality: 0.8)
        }.value
    }
    
    func reset() {
        projectName = ""
        avatarType = .icon
        avatarName = "📁"
        selectedImages = []
        selectedCategory = .other
        nameError = nil
        isCreating = false
        showSuccessAlert = false
        showErrorAlert = false
        errorMessage = ""
    }
    
    func handleError(_ error: Error) {
        if let creationError = error as? ProjectCreationError {
            switch creationError {
            case .invalidName:
                nameError = "project_name_invalid".localized
            case .nameExists:
                nameError = "project_name_exists".localized
            case .saveFailed(let message):
                errorMessage = message
                showErrorAlert = true
            }
        } else {
            errorMessage = String(format: "save_failed_error".localized, error.localizedDescription)
            showErrorAlert = true
        }
    }
}

// MARK: - Project Creation Error
enum ProjectCreationError: Error {
    case invalidName
    case nameExists
    case saveFailed(String)
}

// MARK: - Quick Create Templates
struct QuickCreateTemplate {
    let name: String
    let emoji: String
    let category: ProjectCategory
    
    static let templates = [
        QuickCreateTemplate(name: "work_project".localized, emoji: "💼", category: .work),
        QuickCreateTemplate(name: "study_project".localized, emoji: "📚", category: .study),
        QuickCreateTemplate(name: "life_project".localized, emoji: "🏠", category: .life),
        QuickCreateTemplate(name: "health_project".localized, emoji: "💪", category: .health),
        QuickCreateTemplate(name: "hobby_project".localized, emoji: "🎨", category: .hobby),
        QuickCreateTemplate(name: "travel_project".localized, emoji: "✈️", category: .travel)
    ]
}

// MARK: - Avatar Type Extension
extension AvatarType {
    var displayName: String {
        switch self {
        case .icon:
            return "icon_avatar".localized
        case .image:
            return "image_avatar".localized
        }
    }
    
    var systemImage: String {
        switch self {
        case .icon:
            return "face.smiling"
        case .image:
            return "photo"
        }
    }
}

// MARK: - Project Category Extension
extension ProjectCategory {
    var displayName: String {
        switch self {
        case .work:
            return "work_category".localized
        case .study:
            return "study_category".localized
        case .life:
            return "life_category".localized
        case .health:
            return "health_category".localized
        case .hobby:
            return "hobby_category".localized
        case .travel:
            return "travel_category".localized
        case .other:
            return "other_category".localized
        }
    }
    
    var icon: String {
        switch self {
        case .work:
            return "💼"
        case .study:
            return "📚"
        case .life:
            return "🏠"
        case .health:
            return "💪"
        case .hobby:
            return "🎨"
        case .travel:
            return "✈️"
        case .other:
            return "📁"
        }
    }
    
    var color: Color {
        switch self {
        case .work:
            return .blue
        case .study:
            return .green
        case .life:
            return .orange
        case .health:
            return .red
        case .hobby:
            return .purple
        case .travel:
            return .cyan
        case .other:
            return .gray
        }
    }
}
