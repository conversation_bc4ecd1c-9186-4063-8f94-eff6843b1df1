import Foundation
import SwiftData

@Model
final class Project: @unchecked Sendable {
    @Attribute(.unique) var id: UUID
    var name: String
    var lastUpdated: Date
    @Relationship(deleteRule: .cascade) var chatItems: [ChatItem]?
    var avatarType: AvatarType
    var avatarName: String
    var avatarImageData: Data?
    
    // 缓存字段优化性能
    private var _cachedLatestActivityTime: Date?
    private var _cachedTodayMessageCount: Int?
    private var _cacheTimestamp: Date?
    
    init(name: String, avatarType: AvatarType = .icon, avatarName: String = "folder") {
        self.id = UUID()
        self.name = name
        self.lastUpdated = Date()
        self.chatItems = []
        self.avatarType = avatarType
        self.avatarName = avatarName
    }
    
    var latestMessage: String? {
        chatItems?.max(by: { $0.timestamp < $1.timestamp })?.text
    }
    
    var messageCount: Int {
        chatItems?.count ?? 0
    }
    
    // 优化的今日消息数量计算
    var todayMessageCount: Int {
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        
        // 检查缓存是否有效
        if let cacheTime = _cacheTimestamp,
           let cachedCount = _cachedTodayMessageCount,
           calendar.isDate(cacheTime, inSameDayAs: today) {
            return cachedCount
        }
        
        // 重新计算并缓存
        let count = chatItems?.filter { 
            calendar.isDate($0.timestamp, inSameDayAs: today) 
        }.count ?? 0
        
        _cachedTodayMessageCount = count
        _cacheTimestamp = Date()
        
        return count
    }
    
    func updateLastUpdated() {
        self.lastUpdated = Date()
        // 清除缓存
        invalidateCache()
    }
    
    // 清除缓存方法
    private func invalidateCache() {
        _cachedLatestActivityTime = nil
        _cachedTodayMessageCount = nil
        _cacheTimestamp = nil
    }
    
    var latestActivityTime: Date {
        // 检查缓存是否有效（5分钟内）
        if let cachedTime = _cachedLatestActivityTime,
           let cacheTime = _cacheTimestamp,
           Date().timeIntervalSince(cacheTime) < 300 {
            return cachedTime
        }
        
        // 重新计算并缓存
        let activityTime = chatItems?.max(by: { $0.timestamp < $1.timestamp })?.timestamp ?? lastUpdated
        _cachedLatestActivityTime = activityTime
        _cacheTimestamp = Date()
        
        return activityTime
    }
    
    func isEditable(isSubscribed: Bool = false) -> Bool {
        // 获取所有项目并按最后更新时间排序
        guard let context = modelContext else { return true }
        let descriptor = FetchDescriptor<Project>(
            sortBy: [SortDescriptor(\.lastUpdated, order: .reverse)]
        )
        guard let allProjects = try? context.fetch(descriptor) else { return true }
        
        // 如果是会员，所有项目都可编辑
        if isSubscribed {
            return true
        }
        
        // 非会员只能编辑最近的3个项目
        return allProjects.prefix(3).contains(where: { $0.id == self.id })
    }
}

enum AvatarType: String, Codable, Sendable {
    case icon, image
}

extension Project {
    static func createTutorialProject(in context: ModelContext) -> Project {
        let project = Project(name: "使用教程", avatarType: .icon, avatarName: "📖")
        context.insert(project)
        return project
    }
}
